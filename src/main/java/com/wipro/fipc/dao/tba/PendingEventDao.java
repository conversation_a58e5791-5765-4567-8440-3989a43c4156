package com.wipro.fipc.dao.tba;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import com.wipro.fipc.dao.BaseDao;
import com.wipro.fipc.entity.tba.TbaPendingEvent;

public interface PendingEventDao extends BaseDao<TbaPendingEvent>{

	@Transactional
	@Modifying
	@Query(value = "UPDATE tba.PENDING_EVENT_INQ_CONFIG u set active_flag ='F',updated_by= ?1 ,updated_date = CURRENT_TIMESTAMP where u.process_job_mapping_id = ?2", nativeQuery = true)
	int commonupdate(@Param("updated_by") String updated_by,
			@Param("process_job_mapping_id") long process_job_mapping_id);

	@Query("select count(p) from TbaPendingEvent p where  p.eventName = ?2 and p.eventLongDesc = ?3 and (p.identifier = ?4 or p.identifier is null) and p.tbaFieldName =?5 and p.jsonKey = ?6 and  p.processJobMapping.id =?1  and (p.parNm = ?7 or p.parNm is null) and p.activityId = ?8 and ( p.panelId = ?9 or p.panelId is null ) and  ( p.metaData = ?10 or p.metaData is null ) and  (p.transId = ?11 or p.transId is null )  and  (p.baseKey = ?12 or p.baseKey is null )   and  ( p.subKey = ?13 or p.subKey is null )  and (p.classId = ?14 or p.classId is null )  and (p.identifyFlag = ?15 or p.identifyFlag is null ) and p.manualFlag = ?16 and p.processMultipleOccurrences =?17 and p.criticalEdits = ?18 and activeFlag ='T'  ")
	Long checkForDuplicates(Long id, String eventName, String eventLongDesc, String identifier, String tbaFieldName,
			String jsonKey, String parNm, int activityId, String panelId, String metaData, String transId,
			String baseKey, String subKey, String classId, String identifyFlag, char manualFlag, boolean processMultipleOccurrences, boolean criticalEdits);



}